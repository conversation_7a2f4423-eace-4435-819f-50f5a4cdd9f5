/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "m13.11 7.664 1.78 2.672", key: "go2gg9" }],
  ["path", { d: "m14.162 12.788-3.324 1.424", key: "11x848" }],
  ["path", { d: "m20 4-6.06 1.515", key: "1wxxh7" }],
  ["path", { d: "M3 3v16a2 2 0 0 0 2 2h16", key: "c24i48" }],
  ["circle", { cx: "12", cy: "6", r: "2", key: "1jj5th" }],
  ["circle", { cx: "16", cy: "12", r: "2", key: "4ma0v8" }],
  ["circle", { cx: "9", cy: "15", r: "2", key: "lf2ghp" }]
];
const ChartNetwork = createLucideIcon("chart-network", __iconNode);

export { __iconNode, ChartNetwork as default };
//# sourceMappingURL=chart-network.js.map
