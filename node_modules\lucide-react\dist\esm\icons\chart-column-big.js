/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M3 3v16a2 2 0 0 0 2 2h16", key: "c24i48" }],
  ["rect", { x: "15", y: "5", width: "4", height: "12", rx: "1", key: "q8uenq" }],
  ["rect", { x: "7", y: "8", width: "4", height: "9", rx: "1", key: "sr5ea" }]
];
const ChartColumnBig = createLucideIcon("chart-column-big", __iconNode);

export { __iconNode, ChartColumnBig as default };
//# sourceMappingURL=chart-column-big.js.map
