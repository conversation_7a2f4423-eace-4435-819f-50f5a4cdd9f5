/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M20 4L9 15", key: "1qkx8z" }],
  ["path", { d: "M21 19L3 19", key: "100sma" }],
  ["path", { d: "M9 15L4 10", key: "9zxff7" }]
];
const CheckLine = createLucideIcon("check-line", __iconNode);

export { __iconNode, CheckLine as default };
//# sourceMappingURL=check-line.js.map
