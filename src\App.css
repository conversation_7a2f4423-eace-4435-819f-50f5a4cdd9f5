@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><defs><linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%23059669;stop-opacity:1" /><stop offset="50%" style="stop-color:%2316a34a;stop-opacity:1" /><stop offset="100%" style="stop-color:%23065f46;stop-opacity:1" /></linearGradient><filter id="shadow"><feDropShadow dx="1" dy="1" stdDeviation="0.5" flood-color="%23000000" flood-opacity="0.3"/></filter></defs><path d="M12 2L14 4L16 2L18 4L20 6L18 8L20 10L18 12L16 14L14 12L12 14L10 12L8 14L6 12L4 10L6 8L4 6L6 4L8 2L10 4L12 2Z" fill="url(%23grad1)" stroke="%23374151" stroke-width="0.5" filter="url(%23shadow)"/><circle cx="12" cy="8" r="1.5" fill="%23ffffff" opacity="0.8"/><circle cx="8" cy="12" r="1" fill="%23ffffff" opacity="0.6"/><circle cx="16" cy="12" r="1" fill="%23ffffff" opacity="0.6"/></svg>') 12 12, auto;
  }

  /* Custom cursor for interactive elements */
  button, a, [role="button"], input[type="submit"], input[type="button"] {
    cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28" fill="none"><defs><linearGradient id="grad2" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%23059669;stop-opacity:1" /><stop offset="50%" style="stop-color:%2316a34a;stop-opacity:1" /><stop offset="100%" style="stop-color:%23065f46;stop-opacity:1" /></linearGradient><filter id="shadow2"><feDropShadow dx="2" dy="2" stdDeviation="1" flood-color="%23000000" flood-opacity="0.4"/></filter></defs><path d="M14 2L17 5L21 3L24 6L26 10L23 13L26 16L23 19L21 23L17 21L14 24L11 21L7 23L4 19L2 16L5 13L2 10L5 6L7 3L11 5L14 2Z" fill="url(%23grad2)" stroke="%23374151" stroke-width="0.8" filter="url(%23shadow2)"/><circle cx="14" cy="9" r="2" fill="%23ffffff" opacity="0.9"/><circle cx="9" cy="14" r="1.5" fill="%23ffffff" opacity="0.7"/><circle cx="19" cy="14" r="1.5" fill="%23ffffff" opacity="0.7"/><circle cx="14" cy="19" r="1" fill="%23ffffff" opacity="0.5"/></svg>') 14 14, pointer;
  }

  /* Custom cursor for text inputs */
  input[type="text"], input[type="email"], textarea {
    cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="24" viewBox="0 0 20 24" fill="none"><defs><linearGradient id="grad3" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%23059669;stop-opacity:1" /><stop offset="50%" style="stop-color:%2316a34a;stop-opacity:1" /><stop offset="100%" style="stop-color:%23065f46;stop-opacity:1" /></linearGradient></defs><rect x="9" y="2" width="2" height="20" fill="url(%23grad3)" rx="1"/><rect x="6" y="2" width="8" height="3" fill="url(%23grad3)" rx="1.5"/><rect x="6" y="19" width="8" height="3" fill="url(%23grad3)" rx="1.5"/><circle cx="10" cy="6" r="1" fill="%23ffffff" opacity="0.8"/><circle cx="10" cy="18" r="1" fill="%23ffffff" opacity="0.8"/></svg>') 10 12, text;
  }
}
